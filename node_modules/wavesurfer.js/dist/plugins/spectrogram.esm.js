function e(e,t,s,i){return new(s||(s=Promise))((function(r,n){function a(e){try{o(i.next(e))}catch(e){n(e)}}function l(e){try{o(i.throw(e))}catch(e){n(e)}}function o(e){var t;e.done?r(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,l)}o((i=i.apply(e,t||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;const t=1e3*Math.log(10)/107.939;function s(e){return 2595*Math.log10(1+e/700)}function i(e){return 700*(Math.pow(10,e/2595)-1)}function r(e){return Math.log10(Math.max(1,e))}function n(e){return Math.pow(10,e)}function a(e){let t=26.81*e/(1960+e)-.53;return t<2&&(t+=.15*(2-t)),t>20.1&&(t+=.22*(t-20.1)),t}function l(e){return e<2&&(e=(e-.3)/.85),e>20.1&&(e=(e+4.422)/1.22),(e+.53)/(26.28-e)*1960}function o(e){return t*Math.log10(1+.00437*e)}function h(e){return(Math.pow(10,e/t)-1)/.00437}function c(e,t){switch(t){case"mel":return s(e);case"logarithmic":return r(e);case"bark":return a(e);case"erb":return o(e);default:return e}}function d(e,t,s,i,r){const n=i(0),a=i(s/2),l=Array.from({length:e},(()=>Array(t/2+1).fill(0))),o=s/t;for(let t=0;t<e;t++){const s=r(n+t/e*(a-n)),i=Math.floor(s/o),h=i*o,c=(s-h)/((i+1)*o-h);l[t][i]=1-c,l[t][i+1]=c}return l}function u(e,t){const s=t.length,i=Float32Array.from({length:s},(()=>0));for(let r=0;r<s;r++)for(let s=0;s<e.length;s++)i[r]+=e[s]*t[r][s];return i}const p={gray:()=>{const e=[];for(let t=0;t<256;t++){const s=(255-t)/256;e.push([s,s,s,1])}return e},igray:()=>{const e=[];for(let t=0;t<256;t++){const s=t/256;e.push([s,s,s,1])}return e},roseus:()=>[[.004528,.004341,.004307,1],[.005625,.006156,.00601,1],[.006628,.008293,.008161,1],[.007551,.010738,.01079,1],[.008382,.013482,.013941,1],[.009111,.01652,.017662,1],[.009727,.019846,.022009,1],[.010223,.023452,.027035,1],[.010593,.027331,.032799,1],[.010833,.031475,.039361,1],[.010941,.035875,.046415,1],[.010918,.04052,.053597,1],[.010768,.045158,.060914,1],[.010492,.049708,.068367,1],[.010098,.054171,.075954,1],[.009594,.058549,.083672,1],[.008989,.06284,.091521,1],[.008297,.067046,.099499,1],[.00753,.071165,.107603,1],[.006704,.075196,.11583,1],[.005838,.07914,.124178,1],[.004949,.082994,.132643,1],[.004062,.086758,.141223,1],[.003198,.09043,.149913,1],[.002382,.09401,.158711,1],[.001643,.097494,.167612,1],[.001009,.100883,.176612,1],[514e-6,.104174,.185704,1],[187e-6,.107366,.194886,1],[66e-6,.110457,.204151,1],[186e-6,.113445,.213496,1],[587e-6,.116329,.222914,1],[.001309,.119106,.232397,1],[.002394,.121776,.241942,1],[.003886,.124336,.251542,1],[.005831,.126784,.261189,1],[.008276,.12912,.270876,1],[.011268,.131342,.280598,1],[.014859,.133447,.290345,1],[.0191,.135435,.300111,1],[.024043,.137305,.309888,1],[.029742,.139054,.319669,1],[.036252,.140683,.329441,1],[.043507,.142189,.339203,1],[.050922,.143571,.348942,1],[.058432,.144831,.358649,1],[.066041,.145965,.368319,1],[.073744,.146974,.377938,1],[.081541,.147858,.387501,1],[.089431,.148616,.396998,1],[.097411,.149248,.406419,1],[.105479,.149754,.415755,1],[.113634,.150134,.424998,1],[.121873,.150389,.434139,1],[.130192,.150521,.443167,1],[.138591,.150528,.452075,1],[.147065,.150413,.460852,1],[.155614,.150175,.469493,1],[.164232,.149818,.477985,1],[.172917,.149343,.486322,1],[.181666,.148751,.494494,1],[.190476,.148046,.502493,1],[.199344,.147229,.510313,1],[.208267,.146302,.517944,1],[.217242,.145267,.52538,1],[.226264,.144131,.532613,1],[.235331,.142894,.539635,1],[.24444,.141559,.546442,1],[.253587,.140131,.553026,1],[.262769,.138615,.559381,1],[.271981,.137016,.5655,1],[.281222,.135335,.571381,1],[.290487,.133581,.577017,1],[.299774,.131757,.582404,1],[.30908,.129867,.587538,1],[.318399,.12792,.592415,1],[.32773,.125921,.597032,1],[.337069,.123877,.601385,1],[.346413,.121793,.605474,1],[.355758,.119678,.609295,1],[.365102,.11754,.612846,1],[.374443,.115386,.616127,1],[.383774,.113226,.619138,1],[.393096,.111066,.621876,1],[.402404,.108918,.624343,1],[.411694,.106794,.62654,1],[.420967,.104698,.628466,1],[.430217,.102645,.630123,1],[.439442,.100647,.631513,1],[.448637,.098717,.632638,1],[.457805,.096861,.633499,1],[.46694,.095095,.6341,1],[.47604,.093433,.634443,1],[.485102,.091885,.634532,1],[.494125,.090466,.63437,1],[.503104,.08919,.633962,1],[.512041,.088067,.633311,1],[.520931,.087108,.63242,1],[.529773,.086329,.631297,1],[.538564,.085738,.629944,1],[.547302,.085346,.628367,1],[.555986,.085162,.626572,1],[.564615,.08519,.624563,1],[.573187,.085439,.622345,1],[.581698,.085913,.619926,1],[.590149,.086615,.617311,1],[.598538,.087543,.614503,1],[.606862,.0887,.611511,1],[.61512,.090084,.608343,1],[.623312,.09169,.605001,1],[.631438,.093511,.601489,1],[.639492,.095546,.597821,1],[.647476,.097787,.593999,1],[.655389,.100226,.590028,1],[.66323,.102856,.585914,1],[.670995,.105669,.581667,1],[.678686,.108658,.577291,1],[.686302,.111813,.57279,1],[.69384,.115129,.568175,1],[.7013,.118597,.563449,1],[.708682,.122209,.558616,1],[.715984,.125959,.553687,1],[.723206,.12984,.548666,1],[.730346,.133846,.543558,1],[.737406,.13797,.538366,1],[.744382,.142209,.533101,1],[.751274,.146556,.527767,1],[.758082,.151008,.522369,1],[.764805,.155559,.516912,1],[.771443,.160206,.511402,1],[.777995,.164946,.505845,1],[.784459,.169774,.500246,1],[.790836,.174689,.494607,1],[.797125,.179688,.488935,1],[.803325,.184767,.483238,1],[.809435,.189925,.477518,1],[.815455,.19516,.471781,1],[.821384,.200471,.466028,1],[.827222,.205854,.460267,1],[.832968,.211308,.454505,1],[.838621,.216834,.448738,1],[.844181,.222428,.442979,1],[.849647,.22809,.43723,1],[.855019,.233819,.431491,1],[.860295,.239613,.425771,1],[.865475,.245471,.420074,1],[.870558,.251393,.414403,1],[.875545,.25738,.408759,1],[.880433,.263427,.403152,1],[.885223,.269535,.397585,1],[.889913,.275705,.392058,1],[.894503,.281934,.386578,1],[.898993,.288222,.381152,1],[.903381,.294569,.375781,1],[.907667,.300974,.370469,1],[.911849,.307435,.365223,1],[.915928,.313953,.360048,1],[.919902,.320527,.354948,1],[.923771,.327155,.349928,1],[.927533,.333838,.344994,1],[.931188,.340576,.340149,1],[.934736,.347366,.335403,1],[.938175,.354207,.330762,1],[.941504,.361101,.326229,1],[.944723,.368045,.321814,1],[.947831,.375039,.317523,1],[.950826,.382083,.313364,1],[.953709,.389175,.309345,1],[.956478,.396314,.305477,1],[.959133,.403499,.301766,1],[.961671,.410731,.298221,1],[.964093,.418008,.294853,1],[.966399,.425327,.291676,1],[.968586,.43269,.288696,1],[.970654,.440095,.285926,1],[.972603,.44754,.28338,1],[.974431,.455025,.281067,1],[.976139,.462547,.279003,1],[.977725,.470107,.277198,1],[.979188,.477703,.275666,1],[.980529,.485332,.274422,1],[.981747,.492995,.273476,1],[.98284,.50069,.272842,1],[.983808,.508415,.272532,1],[.984653,.516168,.27256,1],[.985373,.523948,.272937,1],[.985966,.531754,.273673,1],[.986436,.539582,.274779,1],[.98678,.547434,.276264,1],[.986998,.555305,.278135,1],[.987091,.563195,.280401,1],[.987061,.5711,.283066,1],[.986907,.579019,.286137,1],[.986629,.58695,.289615,1],[.986229,.594891,.293503,1],[.985709,.602839,.297802,1],[.985069,.610792,.302512,1],[.98431,.618748,.307632,1],[.983435,.626704,.313159,1],[.982445,.634657,.319089,1],[.981341,.642606,.32542,1],[.98013,.650546,.332144,1],[.978812,.658475,.339257,1],[.977392,.666391,.346753,1],[.97587,.67429,.354625,1],[.974252,.68217,.362865,1],[.972545,.690026,.371466,1],[.97075,.697856,.380419,1],[.968873,.705658,.389718,1],[.966921,.713426,.399353,1],[.964901,.721157,.409313,1],[.962815,.728851,.419594,1],[.960677,.7365,.430181,1],[.95849,.744103,.44107,1],[.956263,.751656,.452248,1],[.954009,.759153,.463702,1],[.951732,.766595,.475429,1],[.949445,.773974,.487414,1],[.947158,.781289,.499647,1],[.944885,.788535,.512116,1],[.942634,.795709,.524811,1],[.940423,.802807,.537717,1],[.938261,.809825,.550825,1],[.936163,.81676,.564121,1],[.934146,.823608,.577591,1],[.932224,.830366,.59122,1],[.930412,.837031,.604997,1],[.928727,.843599,.618904,1],[.927187,.850066,.632926,1],[.925809,.856432,.647047,1],[.92461,.862691,.661249,1],[.923607,.868843,.675517,1],[.92282,.874884,.689832,1],[.922265,.880812,.704174,1],[.921962,.886626,.718523,1],[.92193,.892323,.732859,1],[.922183,.897903,.747163,1],[.922741,.903364,.76141,1],[.92362,.908706,.77558,1],[.924837,.913928,.789648,1],[.926405,.919031,.80359,1],[.92834,.924015,.817381,1],[.930655,.928881,.830995,1],[.93336,.933631,.844405,1],[.936466,.938267,.857583,1],[.939982,.942791,.870499,1],[.943914,.947207,.883122,1],[.948267,.951519,.895421,1],[.953044,.955732,.907359,1],[.958246,.959852,.918901,1],[.963869,.963887,.930004,1],[.969909,.967845,.940623,1],[.976355,.971737,.950704,1],[.983195,.97558,.960181,1],[.990402,.979395,.968966,1],[.99793,.983217,.97692,1]]};function m(e){return e>=1e3?(e/1e3).toFixed(1):Math.round(e).toString()}function f(e){return e>=1e3?"kHz":"Hz"}function b(e,t,s,r,a){const o=c(s,a);return function(e,t){switch(t){case"mel":return i(e);case"logarithmic":return n(e);case"bark":return l(e);case"erb":return h(e);default:return e}}(o+e/t*(c(r,a)-o),a)}function w(e,t,s,i){switch(this.bufferSize=e,this.sampleRate=t,this.bandwidth=2/e*(t/2),this.sinTable=new Float32Array(e),this.cosTable=new Float32Array(e),this.windowValues=new Float32Array(e),this.reverseTable=new Uint32Array(e),this.peakBand=0,this.peak=0,s){case"bartlett":for(let t=0;t<e;t++)this.windowValues[t]=2/(e-1)*((e-1)/2-Math.abs(t-(e-1)/2));break;case"bartlettHann":for(let t=0;t<e;t++)this.windowValues[t]=.62-.48*Math.abs(t/(e-1)-.5)-.38*Math.cos(2*Math.PI*t/(e-1));break;case"blackman":i=i||.16;for(let t=0;t<e;t++)this.windowValues[t]=(1-i)/2-.5*Math.cos(2*Math.PI*t/(e-1))+i/2*Math.cos(4*Math.PI*t/(e-1));break;case"cosine":for(let t=0;t<e;t++)this.windowValues[t]=Math.cos(Math.PI*t/(e-1)-Math.PI/2);break;case"gauss":i=i||.25;for(let t=0;t<e;t++)this.windowValues[t]=Math.pow(Math.E,-.5*Math.pow((t-(e-1)/2)/(i*(e-1)/2),2));break;case"hamming":for(let t=0;t<e;t++)this.windowValues[t]=.54-.46*Math.cos(2*Math.PI*t/(e-1));break;case"hann":case void 0:for(let t=0;t<e;t++)this.windowValues[t]=.5*(1-Math.cos(2*Math.PI*t/(e-1)));break;case"lanczoz":for(let t=0;t<e;t++)this.windowValues[t]=Math.sin(Math.PI*(2*t/(e-1)-1))/(Math.PI*(2*t/(e-1)-1));break;case"rectangular":for(let t=0;t<e;t++)this.windowValues[t]=1;break;case"triangular":for(let t=0;t<e;t++)this.windowValues[t]=2/e*(e/2-Math.abs(t-(e-1)/2));break;default:throw Error("No such window function '"+s+"'")}let r=1,n=e>>1;for(;r<e;){for(let e=0;e<r;e++)this.reverseTable[e+r]=this.reverseTable[e]+n;r<<=1,n>>=1}for(let t=0;t<e;t++)this.sinTable[t]=Math.sin(-Math.PI/t),this.cosTable[t]=Math.cos(-Math.PI/t);this.calculateSpectrum=function(e){const t=this.bufferSize,s=this.cosTable,i=this.sinTable,r=this.reverseTable,n=new Float32Array(t),a=new Float32Array(t),l=2/this.bufferSize,o=Math.sqrt,h=new Float32Array(t/2);let c,d,u;const p=Math.floor(Math.log(t)/Math.LN2);if(Math.pow(2,p)!==t)throw"Invalid buffer size, must be a power of 2.";if(t!==e.length)throw"Supplied buffer is not the same size as defined FFT. FFT Size: "+t+" Buffer Size: "+e.length;let m,f,b,w,y,v,g,M,W=1;for(let s=0;s<t;s++)n[s]=e[r[s]]*this.windowValues[r[s]],a[s]=0;for(;W<t;){m=s[W],f=i[W],b=1,w=0;for(let e=0;e<W;e++){let s=e;for(;s<t;)y=s+W,v=b*n[y]-w*a[y],g=b*a[y]+w*n[y],n[y]=n[s]-v,a[y]=a[s]-g,n[s]+=v,a[s]+=g,s+=W<<1;M=b,b=M*m-w*f,w=M*f+w*m}W<<=1}for(let e=0,s=t/2;e<s;e++)c=n[e],d=a[e],u=l*o(c*c+d*d),u>this.peak&&(this.peakBand=e,this.peak=u),h[e]=u;return h}}class y{constructor(){this.listeners={}}on(e,t,s){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),null==s?void 0:s.once){const s=()=>{this.un(e,s),this.un(e,t)};return this.on(e,s),s}return()=>this.un(e,t)}un(e,t){var s;null===(s=this.listeners[e])||void 0===s||s.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach((e=>e(...t)))}}class v extends y{constructor(e){super(),this.subscriptions=[],this.isDestroyed=!1,this.options=e}onInit(){}_init(e){this.isDestroyed&&(this.subscriptions=[],this.isDestroyed=!1),this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach((e=>e())),this.subscriptions=[],this.isDestroyed=!0,this.wavesurfer=void 0}}function g(e,t){const s=t.xmlns?document.createElementNS(t.xmlns,e):document.createElement(e);for(const[e,i]of Object.entries(t))if("children"===e&&i)for(const[e,t]of Object.entries(i))t instanceof Node?s.appendChild(t):"string"==typeof t?s.appendChild(document.createTextNode(t)):s.appendChild(g(e,t));else"style"===e?Object.assign(s.style,i):"textContent"===e?s.textContent=i:s.setAttribute(e,i.toString());return s}function M(e,t,s){const i=g(e,t||{});return null==s||s.appendChild(i),i}var W=null;try{var Z="undefined"!=typeof module&&"function"==typeof module.require&&module.require("worker_threads")||"function"==typeof __non_webpack_require__&&__non_webpack_require__("worker_threads")||"function"==typeof require&&require("worker_threads");W=Z.Worker}catch(e){}function x(e,t,s){var i=function(e){return Buffer.from(e,"base64").toString("utf8")}(e),r=i.indexOf("\n",10)+1,n=i.substring(r)+"";return function(e){return new W(n,Object.assign({},e,{eval:!0}))}}function S(e,t,s){var i=function(e){return atob(e)}(e),r=i.indexOf("\n",10)+1,n=i.substring(r)+"",a=new Blob([n],{type:"application/javascript"});return URL.createObjectURL(a)}var G="[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0);function X(e,t,s){return G?x(e):function(e){var t;return function(s){return t=t||S(e),new Worker(t,s)}}(e)}var k=X("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");class C extends v{static create(e){return new C(e||{})}constructor(e){var t,s;if(super(e),this.canvases=[],this.useWebWorker=!1,this.worker=null,this.workerPromises=new Map,this.cachedFrequencies=null,this.cachedResampledData=null,this.cachedBuffer=null,this.cachedWidth=0,this.renderTimeout=null,this.isRendering=!1,this.lastZoomLevel=0,this.renderThrottleMs=50,this.zoomThreshold=.05,this.drawnCanvases={},this.pendingBitmaps=new Set,this.isScrollable=!1,this.scrollUnsubscribe=null,this.frequenciesDataUrl=e.frequenciesDataUrl,this.frequenciesDataUrl&&!e.sampleRate)throw new Error("sampleRate option is required when using frequenciesDataUrl");this.container="string"==typeof e.container?document.querySelector(e.container):e.container,this.useWebWorker=!0===e.useWebWorker,this.colorMap=function(e="roseus"){if(e&&"string"!=typeof e){if(e.length<256)throw new Error("Colormap must contain 256 elements");for(let t=0;t<e.length;t++)if(4!==e[t].length)throw new Error("ColorMap entries must contain 4 values");return e}const t=p[e];if(!t)throw Error("No such colormap '"+e+"'");return t()}(e.colorMap),this.fftSamples=e.fftSamples||512,this.height=e.height||200,this.noverlap=e.noverlap||null,this.windowFunc=e.windowFunc||"hann",this.alpha=e.alpha,this.frequencyMin=e.frequencyMin||0,this.frequencyMax=e.frequencyMax||0,this.gainDB=null!==(t=e.gainDB)&&void 0!==t?t:20,this.rangeDB=null!==(s=e.rangeDB)&&void 0!==s?s:80,this.scale=e.scale||"mel",this.numMelFilters=this.fftSamples/2,this.numLogFilters=this.fftSamples/2,this.numBarkFilters=this.fftSamples/2,this.numErbFilters=this.fftSamples/2,e.maxCanvasWidth&&(C.MAX_CANVAS_WIDTH=e.maxCanvasWidth),this.renderThrottleMs=50,this.zoomThreshold=.05,this.createWrapper(),this.createCanvas(),this.useWebWorker&&this.initializeWorker()}initializeWorker(){if("undefined"!=typeof window&&"undefined"!=typeof Worker)try{this.worker=new k,this.worker.onmessage=e=>{const{type:t,id:s,result:i,error:r}=e.data;if("frequenciesResult"===t){const e=this.workerPromises.get(s);e&&(this.workerPromises.delete(s),r?e.reject(new Error(r)):e.resolve(i))}},this.worker.onerror=e=>{console.warn("Spectrogram worker error, falling back to main thread:",e),this.worker=null}}catch(e){console.warn("Failed to initialize worker, falling back to main thread:",e),this.worker=null}else console.warn("Worker not available in this environment, using main thread calculation")}onInit(){this.wrapper||this.createWrapper(),this.canvasContainer||this.createCanvas(),this.container=this.wavesurfer.getWrapper(),this.container.appendChild(this.wrapper),this.wavesurfer.options.fillParent&&Object.assign(this.wrapper.style,{width:"100%",overflowX:"hidden",overflowY:"hidden"}),this.subscriptions.push(this.wavesurfer.on("redraw",(()=>this.throttledRender()))),this.wavesurfer.getDecodedData()&&setTimeout((()=>{this.throttledRender()}),0)}destroy(){this.unAll(),this.wavesurfer&&("function"==typeof this._onReady&&this.wavesurfer.un("ready",this._onReady),"function"==typeof this._onRender&&this.wavesurfer.un("redraw",this._onRender)),this.renderTimeout&&(clearTimeout(this.renderTimeout),this.renderTimeout=null),this.scrollUnsubscribe&&(this.scrollUnsubscribe(),this.scrollUnsubscribe=null),this.pendingBitmaps.clear(),this.worker&&(this.worker.terminate(),this.worker=null),this.cachedFrequencies=null,this.cachedResampledData=null,this.cachedBuffer=null,this.clearCanvases(),this.canvasContainer&&(this.canvasContainer.remove(),this.canvasContainer=null),this.wrapper&&(this.wrapper.remove(),this.wrapper=null),this.labelsEl&&(this.labelsEl.remove(),this.labelsEl=null),this.container=null,this.isRendering=!1,this.lastZoomLevel=0,this.wavesurfer=null,this.util=null,this.options=null,super.destroy()}loadFrequenciesData(t){return e(this,void 0,void 0,(function*(){const e=yield fetch(t);if(!e.ok)throw new Error("Unable to fetch frequencies data");const s=yield e.json();this.drawSpectrogram(s)}))}getFrequenciesData(){return e(this,void 0,void 0,(function*(){var e;const t=null===(e=this.wavesurfer)||void 0===e?void 0:e.getDecodedData();if(!t)return null;if(this.cachedBuffer===t&&this.cachedFrequencies)return this.cachedFrequencies;{const e=yield this.getFrequencies(t);return this.cachedFrequencies=e,this.cachedBuffer=t,e}}))}clearCache(){this.cachedFrequencies=null,this.cachedResampledData=null,this.cachedBuffer=null,this.cachedWidth=0,this.lastZoomLevel=0}createWrapper(){var e,t;this.wrapper=M("div",{style:{display:"block",position:"relative",userSelect:"none"}}),this.options.labels&&(this.labelsEl=M("canvas",{part:"spec-labels",style:{position:"absolute",zIndex:9,width:"55px",height:"100%"}},this.wrapper)),this._onWrapperClick=(e=this.wrapper,t=this.emit.bind(this),s=>{const i=e.getBoundingClientRect(),r=s.clientX-i.left,n=i.width;t("click",r/n)}),this.wrapper.addEventListener("click",this._onWrapperClick)}createCanvas(){this.canvasContainer=M("div",{style:{position:"absolute",left:0,top:0,width:"100%",height:"100%",zIndex:4}},this.wrapper)}createSingleCanvas(e,t,s){const i=M("canvas",{style:{position:"absolute",left:`${Math.round(s)}px`,top:"0",width:`${e}px`,height:`${t}px`,zIndex:4}});return i.width=Math.round(e),i.height=Math.round(t),this.canvasContainer.appendChild(i),i}clearCanvases(){this.canvases.forEach((e=>e.remove())),this.canvases=[],this.drawnCanvases={}}clearExcessCanvases(){Object.keys(this.drawnCanvases).length>C.MAX_NODES&&this.clearCanvases()}throttledRender(){var e;if(this.renderTimeout&&clearTimeout(this.renderTimeout),this.isRendering)return;const t=(null===(e=this.wavesurfer)||void 0===e?void 0:e.options.minPxPerSec)||0;Math.abs(t-this.lastZoomLevel)/Math.max(t,this.lastZoomLevel,1)<this.zoomThreshold&&this.cachedFrequencies?this.renderTimeout=window.setTimeout((()=>{this.fastRender()}),this.renderThrottleMs):this.renderTimeout=window.setTimeout((()=>{this.render()}),this.renderThrottleMs)}render(){return e(this,void 0,void 0,(function*(){var e,t;if(!this.isRendering){this.isRendering=!0;try{if(this.frequenciesDataUrl)yield this.loadFrequenciesData(this.frequenciesDataUrl);else{if(null===(e=this.wavesurfer)||void 0===e?void 0:e.getDecodedData()){yield this.getFrequenciesData();this.drawSpectrogram(this.cachedFrequencies)}}this.lastZoomLevel=(null===(t=this.wavesurfer)||void 0===t?void 0:t.options.minPxPerSec)||0}finally{this.isRendering=!1}}}))}fastRender(){var e;if(!this.isRendering&&this.cachedFrequencies){this.isRendering=!0;try{this.drawSpectrogram(this.cachedFrequencies),this.lastZoomLevel=(null===(e=this.wavesurfer)||void 0===e?void 0:e.options.minPxPerSec)||0}finally{this.isRendering=!1}}}drawSpectrogram(e){var t,s,i;isNaN(e[0][0])||(e=[e]),this.clearCanvases();const r=this.height*e.length;this.wrapper.style.height=r+"px";const n=this.getWidth(),a=Math.min(C.MAX_CANVAS_WIDTH,n);if(0===n||0===r)return;const l=Math.ceil(n/a);let o;const h=(null===(t=e[0])||void 0===t?void 0:t.length)||0;n!==h?this.cachedResampledData&&this.cachedWidth===n?o=this.cachedResampledData:(o=this.efficientResample(e,n),this.cachedResampledData=o,this.cachedWidth=n):o=e;const c=(null===(s=this.buffer)||void 0===s?void 0:s.sampleRate)?this.buffer.sampleRate/2:(this.options.sampleRate||0)/2,d=this.frequencyMin,u=this.frequencyMax,p=u>c,m=p?this.colorMap[this.colorMap.length-1]:null,f=e=>{if(e<0||e>=l)return;if(this.drawnCanvases[e])return;this.drawnCanvases[e]=!0;const t=e*a,s=Math.min(a,n-t);if(s<=0)return;const i=this.createSingleCanvas(s,r,t);this.canvases.push(i);const h=i.getContext("2d");if(h){p&&m&&(h.fillStyle=`rgba(${255*m[0]}, ${255*m[1]}, ${255*m[2]}, ${m[3]})`,h.fillRect(0,0,s,r));for(let e=0;e<o.length;e++)this.drawSpectrogramSegment(o[e],h,s,this.height,e*this.height,t,n,c,d,u)}};if(this.isScrollable=n>this.getWrapperWidth(),this.scrollUnsubscribe&&(this.scrollUnsubscribe(),this.scrollUnsubscribe=null),!this.isScrollable||l<=3)for(let e=0;e<l;e++)f(e);else{const e=()=>{var e;const t=null===(e=this.wavesurfer)||void 0===e?void 0:e.getWrapper();if(!t)return;const s=t.scrollLeft||0,i=t.clientWidth||0,r=Math.max(0,s-.5*i),a=Math.min(n,s+1.5*i),o=Math.floor(r/n*l),h=Math.min(Math.ceil(a/n*l),l-1);Object.keys(this.drawnCanvases).length>C.MAX_NODES&&this.clearExcessCanvases();for(let e=o;e<=h;e++)f(e)};e();let t=null;const s=()=>{t&&clearTimeout(t),t=window.setTimeout(e,16)},r=null===(i=this.wavesurfer)||void 0===i?void 0:i.getWrapper();r&&(r.addEventListener("scroll",s,{passive:!0}),this.scrollUnsubscribe=()=>{r.removeEventListener("scroll",s),t&&clearTimeout(t)})}this.options.labels&&this.loadLabels(this.options.labelsBackground,"12px","12px","",this.options.labelsColor,this.options.labelsHzColor||this.options.labelsColor,"center","#specLabels",e.length),this.emit("ready")}drawSpectrogramSegment(e,t,s,i,r,n,a,l,o,h){const d=e[0].length,u=Math.floor(n/a*e.length),p=Math.min(Math.ceil((n+s)/a*e.length),e.length),m=e.slice(u,p);if(0===m.length)return;const f=m.length,b=new ImageData(f,d),w=b.data;this.fillImageDataQuality(w,m,f,d);const y=c(o,this.scale)/c(l,this.scale),v=c(h,this.scale)/c(l,this.scale),g=Math.min(1,v),M=createImageBitmap(b,0,Math.round(d*(1-g)),f,Math.round(d*(g-y)));this.pendingBitmaps.add(M),M.then((e=>{if(this.pendingBitmaps.delete(M),t.canvas.parentNode){const n=i*g/v,a=r+i*(1-g/v);t.drawImage(e,0,a,s,n),"close"in e&&e.close()}})).catch((e=>{this.pendingBitmaps.delete(M)}))}getWidth(){return this.wavesurfer.getWrapper().offsetWidth}getWrapperWidth(){var e,t;return(null===(t=null===(e=this.wavesurfer)||void 0===e?void 0:e.getWrapper())||void 0===t?void 0:t.clientWidth)||0}calculateFrequenciesWithWorker(t){return e(this,void 0,void 0,(function*(){var e,s;if(!this.worker)throw new Error("Worker not available");const i=this.fftSamples,r=(null!==(e=this.options.splitChannels)&&void 0!==e?e:null===(s=this.wavesurfer)||void 0===s?void 0:s.options.splitChannels)?t.numberOfChannels:1;let n=this.noverlap;if(!n){const e=this.getWidth(),s=t.length/e;n=Math.max(0,Math.round(i-s))}const a=[];for(let e=0;e<r;e++)a.push(t.getChannelData(e));const l=`${Date.now()}_${Math.random()}`,o=new Promise(((e,t)=>{this.workerPromises.set(l,{resolve:e,reject:t}),setTimeout((()=>{this.workerPromises.has(l)&&(this.workerPromises.delete(l),t(new Error("Worker timeout")))}),3e4)}));return this.worker.postMessage({type:"calculateFrequencies",id:l,audioData:a,options:{startTime:0,endTime:t.duration,sampleRate:t.sampleRate,fftSamples:this.fftSamples,windowFunc:this.windowFunc,alpha:this.alpha,noverlap:n,scale:this.scale,gainDB:this.gainDB,rangeDB:this.rangeDB,splitChannels:this.options.splitChannels||!1}}),o}))}getFrequencies(t){return e(this,void 0,void 0,(function*(){var e,c;if(this.frequencyMax=this.frequencyMax||t.sampleRate/2,this.buffer=t,!t)return[];if(this.useWebWorker&&this.worker)try{return yield this.calculateFrequenciesWithWorker(t)}catch(e){console.warn("Worker calculation failed, falling back to main thread:",e)}const p=this.fftSamples,m=(null!==(e=this.options.splitChannels)&&void 0!==e?e:null===(c=this.wavesurfer)||void 0===c?void 0:c.options.splitChannels)?t.numberOfChannels:1,f=t.sampleRate,b=[];let y=this.noverlap;if(!y){const e=this.getWidth(),s=t.length/e;y=Math.max(0,Math.round(p-s))}let v=y||Math.max(0,Math.round(.5*p));const g=.5*p;v=Math.min(v,g);const M=Math.max(64,.25*p),W=Math.max(M,p-v),Z=new w(p,f,this.windowFunc,this.alpha),x=this.fftSamples/2,S=function(e,t,c,u){switch(e){case"mel":return d(t,c,u,s,i);case"logarithmic":return d(t,c,u,r,n);case"bark":return d(t,c,u,a,l);case"erb":return d(t,c,u,o,h);default:return null}}(this.scale,x,this.fftSamples,f);for(let e=0;e<m;e++){const s=t.getChannelData(e),i=[];for(let e=0;e+p<s.length;e+=W){const t=s.slice(e,e+p);let r=Z.calculateSpectrum(t);S&&(r=u(r,S));const n=new Uint8Array(r.length),a=this.gainDB+this.rangeDB;for(let e=0;e<r.length;e++){const t=r[e]>1e-12?r[e]:1e-12,s=20*Math.log10(t);s<-a?n[e]=0:s>-this.gainDB?n[e]=255:n[e]=Math.round((s+this.gainDB)/this.rangeDB*255)}i.push(n)}b.push(i)}return b}))}loadLabels(e,t,s,i,r,n,a,l,o){e=e||"rgba(68,68,68,0)",t=t||"12px",s=s||"12px",i=i||"Helvetica",r=r||"#fff",n=n||"#fff",a=a||"center";const h=this.height||512,c=h/256*5;this.frequencyMin;this.frequencyMax;const d=this.labelsEl.getContext("2d"),u=window.devicePixelRatio;if(this.labelsEl.height=this.height*o*u,this.labelsEl.width=55*u,d.scale(u,u),d)for(let l=0;l<o;l++){let o;for(d.fillStyle=e,d.fillRect(0,l*h,55,(1+l)*h),d.fill(),o=0;o<=c;o++){d.textAlign=a,d.textBaseline="middle";const e=b(o,c,this.frequencyMin,this.frequencyMax,this.scale),u=m(e),p=f(e),w=16;let y=(1+l)*h-o/c*h;y=Math.min(Math.max(y,l*h+10),(1+l)*h-10),d.fillStyle=n,d.font=s+" "+i,d.fillText(p,w+24,y),d.fillStyle=r,d.font=t+" "+i,d.fillText(u,w,y)}}}efficientResample(e,t){return e.map((e=>this.resampleChannel(e,t)))}resampleChannel(e,t){var s;const i=e.length,r=(null===(s=e[0])||void 0===s?void 0:s.length)||0;if(i===t||0===t)return e;const n=i/t,a=new Array(t);if(n>=1)for(let s=0;s<t;s++){const t=Math.floor(s*n),l=Math.min(Math.ceil((s+1)*n),i),o=l-t,h=new Uint8Array(r);if(1===o)h.set(e[t]);else for(let s=0;s<r;s++){let i=0;for(let r=t;r<l;r++)i+=e[r][s];h[s]=Math.round(i/o)}a[s]=h}else for(let s=0;s<t;s++){const t=s*n,l=Math.floor(t),o=Math.min(l+1,i-1),h=t-l,c=new Uint8Array(r);if(0===h||l===o)c.set(e[l]);else{const t=e[l],s=e[o],i=1-h;for(let e=0;e<r;e++)c[e]=Math.round(t[e]*i+s[e]*h)}a[s]=c}return a}fillImageDataQuality(e,t,s,i){const r=this.colorMap;for(let n=0;n<s;n++){const a=t[n];for(let t=0;t<i;t++){const l=r[a[t]],o=4*((i-t-1)*s+n);e[o]=255*l[0],e[o+1]=255*l[1],e[o+2]=255*l[2],e[o+3]=255*l[3]}}}}C.MAX_CANVAS_WIDTH=3e4,C.MAX_NODES=10;export{C as default};
