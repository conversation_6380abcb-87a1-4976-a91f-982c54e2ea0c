import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client (for API routes)
export const createServerClient = () => {
  return createClient(
    supabaseUrl,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )
}

// Database types (will be updated after schema creation)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          owner_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          owner_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          owner_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      audio_files: {
        Row: {
          id: string
          project_id: string
          filename: string
          original_filename: string
          file_size: number
          duration: number | null
          version_name: string
          storage_path: string
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          filename: string
          original_filename: string
          file_size: number
          duration?: number | null
          version_name: string
          storage_path: string
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          filename?: string
          original_filename?: string
          file_size?: number
          duration?: number | null
          version_name?: string
          storage_path?: string
          created_at?: string
        }
      }
      comments: {
        Row: {
          id: string
          audio_file_id: string
          timestamp: number
          content: string
          commenter_name: string | null
          is_resolved: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          audio_file_id: string
          timestamp: number
          content: string
          commenter_name?: string | null
          is_resolved?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          audio_file_id?: string
          timestamp?: number
          content?: string
          commenter_name?: string | null
          is_resolved?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      share_links: {
        Row: {
          id: string
          project_id: string
          token: string
          expires_at: string | null
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          token: string
          expires_at?: string | null
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          token?: string
          expires_at?: string | null
          is_active?: boolean
          created_at?: string
        }
      }
    }
  }
}
